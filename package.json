{"name": "ai-adventurer", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "vercel-build": "vite build", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@google/genai": "^0.9.0", "@google/generative-ai": "^0.24.1", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.7.3", "@types/node": "^22.15.30", "@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue/eslint-config-typescript": "^12.0.0", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.24.0", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "vite": "^5.2.0", "vite-plugin-vue-devtools": "^7.6.0", "vue-tsc": "^2.0.6"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended", "@vue/typescript/recommended"], "parserOptions": {"ecmaVersion": "latest", "parser": "@typescript-eslint/parser", "sourceType": "module"}, "rules": {"vue/multi-word-component-names": "off", "vue/no-multiple-template-root": "off"}, "ignorePatterns": ["dist", "node_modules"]}}