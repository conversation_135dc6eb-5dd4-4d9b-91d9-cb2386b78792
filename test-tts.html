<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a202c;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #4a5568;
            border-radius: 8px;
        }
        button {
            background: #9f7aea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #805ad5;
        }
        button:disabled {
            background: #4a5568;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .status.success {
            background: #48bb78;
        }
        .status.error {
            background: #f56565;
        }
        .status.info {
            background: #4299e1;
        }
        textarea {
            width: 100%;
            height: 100px;
            background: #2d3748;
            color: white;
            border: 1px solid #4a5568;
            border-radius: 5px;
            padding: 10px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <h1>AI Adventurer TTS Test</h1>
    
    <div class="test-section">
        <h2>Environment Check</h2>
        <div id="env-status"></div>
        <button onclick="checkEnvironment()">Check Environment</button>
    </div>

    <div class="test-section">
        <h2>Gemini TTS Test</h2>
        <textarea id="tts-text" placeholder="Enter text to speak...">Hello! This is a test of the Gemini TTS system. Can you hear me clearly?</textarea>
        <br>
        <button onclick="testGeminiTTS()" id="tts-btn">Test Gemini TTS</button>
        <div id="tts-status"></div>
    </div>

    <div class="test-section">
        <h2>Browser TTS Test (Fallback)</h2>
        <textarea id="browser-tts-text" placeholder="Enter text to speak...">This is a test of the browser's built-in speech synthesis.</textarea>
        <br>
        <button onclick="testBrowserTTS()" id="browser-tts-btn">Test Browser TTS</button>
        <div id="browser-tts-status"></div>
    </div>

    <script type="module">
        // Check environment
        function checkEnvironment() {
            const statusDiv = document.getElementById('env-status');
            let status = '<div class="status info">';
            
            // Check API key
            const apiKey = import.meta.env?.VITE_GEMINI_API_KEY;
            status += `<p>API Key: ${apiKey ? '✅ Present' : '❌ Missing'}</p>`;
            
            // Check browser features
            status += `<p>Web Audio API: ${window.AudioContext ? '✅ Supported' : '❌ Not supported'}</p>`;
            status += `<p>Speech Recognition: ${window.SpeechRecognition || window.webkitSpeechRecognition ? '✅ Supported' : '❌ Not supported'}</p>`;
            status += `<p>Speech Synthesis: ${window.speechSynthesis ? '✅ Supported' : '❌ Not supported'}</p>`;
            
            status += '</div>';
            statusDiv.innerHTML = status;
        }

        // Test Gemini TTS
        async function testGeminiTTS() {
            const text = document.getElementById('tts-text').value;
            const statusDiv = document.getElementById('tts-status');
            const btn = document.getElementById('tts-btn');
            
            if (!text.trim()) {
                statusDiv.innerHTML = '<div class="status error">Please enter some text to test.</div>';
                return;
            }
            
            btn.disabled = true;
            statusDiv.innerHTML = '<div class="status info">Testing Gemini TTS...</div>';
            
            try {
                // Import the TTS function
                const { speakText } = await import('./src/services/geminiTts.ts');
                
                await speakText(text, {
                    voiceName: 'Kore',
                    model: 'gemini-2.5-flash-preview-tts'
                });
                
                statusDiv.innerHTML = '<div class="status success">✅ Gemini TTS test completed successfully!</div>';
            } catch (error) {
                console.error('Gemini TTS error:', error);
                statusDiv.innerHTML = `<div class="status error">❌ Gemini TTS failed: ${error.message}</div>`;
            } finally {
                btn.disabled = false;
            }
        }

        // Test Browser TTS
        function testBrowserTTS() {
            const text = document.getElementById('browser-tts-text').value;
            const statusDiv = document.getElementById('browser-tts-status');
            const btn = document.getElementById('browser-tts-btn');
            
            if (!text.trim()) {
                statusDiv.innerHTML = '<div class="status error">Please enter some text to test.</div>';
                return;
            }
            
            if (!window.speechSynthesis) {
                statusDiv.innerHTML = '<div class="status error">❌ Speech synthesis not supported in this browser.</div>';
                return;
            }
            
            btn.disabled = true;
            statusDiv.innerHTML = '<div class="status info">Testing browser TTS...</div>';
            
            try {
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.rate = 1.0;
                utterance.pitch = 1.0;
                utterance.volume = 1.0;
                
                utterance.onend = () => {
                    statusDiv.innerHTML = '<div class="status success">✅ Browser TTS test completed successfully!</div>';
                    btn.disabled = false;
                };
                
                utterance.onerror = (error) => {
                    statusDiv.innerHTML = `<div class="status error">❌ Browser TTS failed: ${error.error}</div>`;
                    btn.disabled = false;
                };
                
                window.speechSynthesis.speak(utterance);
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ Browser TTS failed: ${error.message}</div>`;
                btn.disabled = false;
            }
        }

        // Make functions global
        window.checkEnvironment = checkEnvironment;
        window.testGeminiTTS = testGeminiTTS;
        window.testBrowserTTS = testBrowserTTS;

        // Auto-check environment on load
        checkEnvironment();
    </script>
</body>
</html>
