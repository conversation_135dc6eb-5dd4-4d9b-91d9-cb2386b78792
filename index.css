#bg {
  position:fixed;
  width: 100vw;
  height: 100vh;
  left: 0;
  top: 0;
  pointer-events: none;
  background: white;
}

.rounded-wow {
  border-radius:34px;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.25s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.elasticBottom-enter-active,
.elasticBottom-leave-active {
  pointer-events: none;
  transition:
    opacity 1.3s cubic-bezier(0.68, -0.55, 0.265, 1.55),
    transform 1.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.elasticBottom-enter-from,
.elasticBottom-leave-to {
  opacity: 0;
  transform: translate(0, 70px);
}

button,
.button {
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

button:hover,
.button:hover {
  transform: scale(0.97);
}

button:active,
.button:active {
  transform: translateY(6px);
}

#voiceSelect {
  border-right: 16px solid transparent;
}

@keyframes wave {
  0%, 100% { height: 20%; }
  25% { height: 80%; }
  50% { height: 40%; }
  75% { height: 60%; }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
.animate-gradient {
  background: linear-gradient(90deg, #eee, #fff, #efefef, #fefefe, #eee);
  background-size: 300% 300%;
  animation: gradient 10s ease infinite;
}

.animate-wave {
  animation: wave 1s ease-in-out infinite;
}

.header {
  writing-mode: vertical-lr;
  transform: rotate(-180deg);
  text-align: center;
  font-size:20px;
  opacity:.5;
}