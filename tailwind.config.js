/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#9f7aea',
          dark: '#805ad5',
        },
        secondary: '#4fd1c5',
        accent: '#f6ad55',
        background: '#1a202c',
        text: {
          DEFAULT: '#e2e8f0',
          secondary: '#a0aec0',
        },
        success: '#48bb78',
        danger: '#f56565',
        warning: '#ecc94b',
        info: '#4299e1',
      },
      fontFamily: {
        sans: ['Grandstander', 'sans-serif'],
        mono: ['Roboto Mono', 'monospace'],
      },
      animation: {
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-slow': 'bounce 3s infinite',
      },
    },
  },
  plugins: [],
}
