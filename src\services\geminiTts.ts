// Check if we have the API key
const API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
console.log('Gemini TTS - API Key check:', API_KEY ? 'Present' : 'Missing');

if (!API_KEY) {
  console.error('VITE_GEMINI_API_KEY is not set. Available env vars:', Object.keys(import.meta.env));
  throw new Error('VITE_GEMINI_API_KEY is not set. Gemini TTS requires an API key.');
}

// Supported voice names for Gemini TTS
type VoiceName = 'Kore' | 'Cora' | 'Ava' | 'Nova' | 'Liam' | 'Ethan' | 'Sophia' | 'Emma';

interface GeminiTTSOptions {
  voiceName?: VoiceName;
  speakingRate?: number;
  pitch?: number;
  volumeGainDb?: number;
  model?: string;
}

/**
 * Converts text to speech using Gemini TTS API
 * @param text The text to convert to speech
 * @param options Configuration options for TTS
 * @returns A promise that resolves to an AudioBuffer
 */
async function textToSpeech(
  text: string,
  options: GeminiTTSOptions = {}
): Promise<AudioBuffer> {
  if (!text?.trim()) {
    throw new Error('No text provided for TTS');
  }

  const {
    voiceName = 'Kore',
    model = 'gemini-2.5-flash-preview-tts'
  } = options;

  try {
    console.log('Generating TTS for text:', text.substring(0, 100) + '...');

    // Use the GoogleGenAI client with correct format (based on working example)
    const { GoogleGenAI } = await import('@google/genai');
    const ai = new GoogleGenAI({ apiKey: API_KEY });

    const response = await ai.models.generateContent({
      model: model,
      contents: [{ parts: [{ text: text }] }],
      config: {
        responseModalities: ['AUDIO'],
        speechConfig: {
          voiceConfig: {
            prebuiltVoiceConfig: { voiceName: voiceName },
          },
        },
      },
    });

    console.log('Gemini TTS response received');

    // The audio data should be in the first candidate's content parts as inline data
    const audioData = response.candidates?.[0]?.content?.parts?.[0]?.inlineData?.data;
    if (!audioData) {
      console.error('No audio data in response:', response);
      throw new Error('No audio data received from Gemini TTS');
    }

    console.log('Audio data received, decoding...');

    // Decode base64 audio data (raw PCM) using browser-compatible methods
    const binaryString = atob(audioData);
    const audioBytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      audioBytes[i] = binaryString.charCodeAt(i);
    }

    // Create audio context
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

    // Create AudioBuffer from the raw PCM data (24kHz, mono, 16-bit)
    const int16Array = new Int16Array(audioBytes.buffer, audioBytes.byteOffset, audioBytes.byteLength / 2);
    const webAudioBuffer = audioContext.createBuffer(1, int16Array.length, 24000);
    const channelData = webAudioBuffer.getChannelData(0);

    // Convert 16-bit PCM to float32 for Web Audio API
    for (let i = 0; i < int16Array.length; i++) {
      channelData[i] = int16Array[i] / 32768.0; // Convert to [-1, 1] range
    }

    console.log('Audio buffer created successfully');
    return webAudioBuffer;

  } catch (error) {
    console.error('Error in Gemini TTS:', error);
    throw new Error(`Gemini TTS failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Speaks the given text using Gemini TTS
 * @param text The text to speak
 * @param options Configuration options for TTS
 * @returns A promise that resolves when speech is complete
 * @throws Error if Gemini TTS fails
 */
export async function speakText(
  text: string,
  options: GeminiTTSOptions = {}
): Promise<void> {
  if (!text?.trim()) {
    console.warn('No text to speak');
    return;
  }

  try {
    console.log('Starting TTS for:', text.substring(0, 50) + '...');

    // Get the audio buffer from Gemini TTS
    const audioBuffer = await textToSpeech(text, options);

    // Create audio context and source
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

    // Resume audio context if it's suspended (required by some browsers)
    if (audioContext.state === 'suspended') {
      await audioContext.resume();
    }

    const source = audioContext.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(audioContext.destination);

    console.log('Playing TTS audio...');

    // Play the audio
    return new Promise((resolve, reject) => {
      source.onended = () => {
        console.log('TTS audio playback completed');
        // Don't close the audio context immediately, just resolve
        resolve();
      };

      // Note: AudioBufferSourceNode doesn't have onerror event
      // Error handling is done in the try-catch block below

      try {
        source.start(0);
      } catch (error) {
        console.error('Error starting audio playback:', error);
        reject(new Error(`Audio playback failed: ${error instanceof Error ? error.message : 'Unknown error'}`));
      }
    });

  } catch (error) {
    console.error('Gemini TTS failed:', error);
    throw error;
  }
}

// Export the textToSpeech function as well for direct use
export { textToSpeech };
