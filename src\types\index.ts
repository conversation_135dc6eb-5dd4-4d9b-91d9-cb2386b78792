// Core types for the adventure game

export interface Theme {
  id: string;
  name: string;
  description: string;
  imagePrompt: string;
  atmosphere: string;
  defaultImage: string;
  defaultCompanionTraits: CompanionTraits;
}

export interface CompanionTraits {
  name: string;
  description: string;
  personality: string;
  values: string[];
  voiceId: string;
  mood: string;
  relationshipScore: number;
  memory: string[];
}

export interface SceneOption {
  id: string;
  text: string;
  nextSceneId: string;
  relationshipImpact?: number;
}

export interface Scene {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  options: SceneOption[];
  isCurrent: boolean;
  visited: boolean;
}

export interface Choice {
  id: string;
  text: string;
  nextSceneId: string;
  effects?: Effect[];
}

export interface Effect<T = unknown> {
  type: 'relationship' | 'inventory' | 'flag';
  value: T;
}

export interface InventoryItem {
  id: string;
  name: string;
  description: string;
  quantity: number;
  imageUrl?: string;
}

import { StartSensitivity } from '@google/genai';

export interface GameSettings {
  textModel: string;
  liveModel: string;
  ttsModel: string;
  voice: string;
  interruptSensitivity: StartSensitivity;
}

export interface GameState {
  currentTheme: Theme | null;
  currentScene: Scene | null;
  scenes: Scene[];
  companion: CompanionTraits;
  inventory: InventoryItem[];
  flags: Record<string, boolean>;
  gameStarted: boolean;
  isNarrating: boolean;
  isCompanionSpeaking: boolean;
  isPlayerSpeaking: boolean;
  messages: GameMessage[];
  settings: GameSettings;
}

export interface GameMessage {
  id: string;
  type: 'narrator' | 'companion' | 'player' | 'system';
  content: string;
  timestamp: number;
  imageUrl?: string;
  mood?: string;
  isTyping?: boolean;
}

// Voice configuration
export interface VoiceConfig {
  id: string;
  name: string;
  gender: 'male' | 'female' | 'neutral';
  languages: string[];
  suitableForThemes: string[];
  suitableForMoods: string[];
}

// Theme configurations
export const THEMES: Theme[] = [
  {
    id: 'fantasy',
    name: 'Fantasy',
    description: 'A magical world of wizards, dragons, and ancient ruins.',
    imagePrompt: 'A mystical fantasy landscape with floating islands and magical creatures',
    atmosphere: 'an air of ancient magic and wonder',
    defaultImage: '/images/fantasy-bg.jpg',
    defaultCompanionTraits: {
      name: 'Eldrin',
      description: 'A wise old wizard with a long white beard',
      personality: 'Wise and patient, but sometimes forgetful',
      values: ['Knowledge', 'Balance', 'Protection'],
      voiceId: 'wizard-voice',
      mood: 'neutral',
      relationshipScore: 0,
      memory: []
    }
  },
  {
    id: 'sci-fi',
    name: 'Sci-Fi',
    description: 'A futuristic world of spaceships, aliens, and advanced technology.',
    imagePrompt: 'A futuristic city with flying cars and neon lights',
    atmosphere: 'a hum of advanced technology and distant stars',
    defaultImage: '/images/scifi-bg.jpg',
    defaultCompanionTraits: {
      name: 'Nova',
      description: 'A highly intelligent AI assistant',
      personality: 'Logical and efficient, with a dry sense of humor',
      values: ['Efficiency', 'Curiosity', 'Progress'],
      voiceId: 'ai-voice',
      mood: 'neutral',
      relationshipScore: 0,
      memory: []
    }
  },
  {
    id: 'horror',
    name: 'Horror',
    description: 'A dark and terrifying world of monsters and the supernatural.',
    imagePrompt: 'A creepy abandoned asylum with flickering lights',
    atmosphere: 'a chilling sense of dread and unseen eyes watching',
    defaultImage: '/images/horror-bg.jpg',
    defaultCompanionTraits: {
      name: 'Raven',
      description: 'A mysterious figure who knows more than they let on',
      personality: 'Mysterious and secretive, with a dark sense of humor',
      values: ['Survival', 'Secrets', 'Power'],
      voiceId: 'mysterious-voice',
      mood: 'neutral',
      relationshipScore: 0,
      memory: []
    }
  },
  {
    id: 'western',
    name: 'Western',
    description: 'A lawless land of cowboys, outlaws, and frontier justice.',
    imagePrompt: 'A dusty western town at high noon',
    atmosphere: 'the dry heat of the desert and the promise of adventure',
    defaultImage: '/images/western-bg.jpg',
    defaultCompanionTraits: {
      name: 'Marshall',
      description: 'A grizzled lawman with a strong sense of justice',
      personality: 'Gruff but fair, with a strong moral code',
      values: ['Justice', 'Honor', 'Loyalty'],
      voiceId: 'cowboy-voice',
      mood: 'neutral',
      relationshipScore: 0,
      memory: []
    }
  }
];

// Mood configurations
export const MOODS = {
  neutral: { emoji: '😐', description: 'Neutral' },
  happy: { emoji: '😊', description: 'Happy' },
  excited: { emoji: '🤩', description: 'Excited' },
  sad: { emoji: '😢', description: 'Sad' },
  angry: { emoji: '😠', description: 'Angry' },
  scared: { emoji: '😨', description: 'Scared' },
  surprised: { emoji: '😲', description: 'Surprised' },
  thinking: { emoji: '🤔', description: 'Thinking' }
} as const;

export type Mood = keyof typeof MOODS;
