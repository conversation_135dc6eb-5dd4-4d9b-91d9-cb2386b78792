<template>
  <div class="theme-selector">
    <h1 class="text-4xl font-bold text-center mb-8 text-white">Adventure Awaits</h1>
    <p class="text-xl text-center mb-12 text-gray-300 max-w-2xl mx-auto">
      Choose your adventure theme and embark on an unforgettable journey with your AI companion.
    </p>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6 max-w-4xl mx-auto">
      <div 
        v-for="theme in themes" 
        :key="theme.id"
        class="theme-card relative overflow-hidden rounded-xl cursor-pointer transform transition-all duration-300 hover:scale-105"
        @click="selectTheme(theme.id)"
      >
        <div 
          class="theme-image h-48 bg-cover bg-center"
          :style="{ backgroundImage: `url(${getThemeImage(theme.id)})` }"
        ></div>
        <div class="p-6 bg-white bg-opacity-10 backdrop-blur-sm">
          <h2 class="text-2xl font-bold text-white mb-2">{{ theme.name }}</h2>
          <p class="text-gray-300">{{ theme.description }}</p>
        </div>
        <div class="absolute inset-0 border-2 border-transparent hover:border-purple-400 rounded-xl pointer-events-none transition-colors"></div>
      </div>
    </div>
    
    <div class="mt-12 text-center">
      <button 
        class="px-8 py-4 bg-purple-600 hover:bg-purple-700 text-white font-bold rounded-full text-lg transition-colors"
        @click="selectRandomTheme"
      >
        🎲 Surprise Me
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { THEMES } from '../types/index';

// Preload theme images
const themeImages: Record<string, string> = {
  fantasy: 'https://source.unsplash.com/random/800x600?fantasy,landscape,magic',
  scifi: 'https://source.unsplash.com/random/800x600?scifi,future,city',
  western: 'https://source.unsplash.com/random/800x600?western,desert,cowboy',
  magicalRealism: 'https://source.unsplash.com/random/800x600?magical,realism,whimsical'
};

export default defineComponent({
  name: 'ThemeSelector',
  
  setup(_, { emit }) {
    const themes = THEMES;
    
    const getThemeImage = (themeId: string): string => {
      return themeImages[themeId] || themeImages.fantasy;
    };
    
    const selectTheme = (themeId: string) => {
      emit('theme-selected', themeId);
    };
    
    const selectRandomTheme = () => {
      const randomIndex = Math.floor(Math.random() * themes.length);
      selectTheme(themes[randomIndex].id);
    };
    
    return {
      themes,
      getThemeImage,
      selectTheme,
      selectRandomTheme
    };
  }
});
</script>

<style scoped>
.theme-selector {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.theme-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 
              0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.theme-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 
              0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.theme-image {
  width: 100%;
  height: 200px;
  background-size: cover;
  background-position: center;
  transition: all 0.5s ease;
}

.theme-card:hover .theme-image {
  transform: scale(1.05);
}
</style>
