import { LiveServerMessage, Modality, StartSensitivity, EndSensitivity } from '@google/genai';

export interface Session {
  send: (data: { text?: string; audio?: { data: string; mimeType: string } }) => Promise<void>;
  close: () => Promise<void>;
  // Add other session methods as needed
}

export interface DialogServiceCallbacks {
  onopen: () => void;
  onmessage: (message: LiveServerMessage) => Promise<void> | void;
  onerror: (error: Error) => void;
  onclose: () => void;
}

export interface DialogConfig {
  textModel: string;
  ttsModel: string;
  responseModalities: Modality[];
  realtimeInputConfig: {
    startSensitivity: StartSensitivity;
    endSensitivity: EndSensitivity;
    voice: string;
  };
  speechConfig: {
    voice: string;
  };
}

export class DialogService {
  private client: any; // GoogleGenAI client

  constructor(apiKey: string) {
    // Initialize the GoogleGenAI client with the provided API key
    this.client = new (window as any).google.genai.DialogService({
      apiKey: apiKey,
    });
  }

  async connect(config: DialogConfig, callbacks: DialogServiceCallbacks): Promise<Session> {
    try {
      const session = await this.client.live.connect({
        model: config.textModel, // Use text model for the main dialog
        callbacks: {
          onopen: callbacks.onopen,
          onmessage: callbacks.onmessage,
          onerror: callbacks.onerror,
          onclose: callbacks.onclose,
        },
        config: {
          responseModalities: config.responseModalities,
          realtimeInputConfig: config.realtimeInputConfig,
          speechConfig: {
            ...config.speechConfig,
            model: config.ttsModel // Use TTS model for speech generation
          },
        },
      });
      return session;
    } catch (e) {
      console.error('Failed to connect to dialog service:', e);
      throw e;
    }
  }

  async sendRealtimeInput(session: Session, data: { audio: string; mimeType: string }): Promise<void> {
    try {
      await session.send({
        audio: {
          data: data.audio,
          mimeType: data.mimeType,
        },
      });
    } catch (e) {
      console.error('Error sending realtime input:', e);
      throw e;
    }
  }

  async sendClientContent(session: Session, content: { text: string }): Promise<void> {
    try {
      await session.send({
        text: content.text,
      });
    } catch (e) {
      console.error('Error sending client content:', e);
      throw e;
    }
  }

  async closeSession(session: Session): Promise<void> {
    try {
      await session.close();
    } catch (e) {
      console.error('Error closing session:', e);
      throw e;
    }
  }
}
