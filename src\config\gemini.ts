// Model configurations
export const DEFAULT_DIALOG_MODEL = 'gemini-1.5-flash';
export const DEFAULT_IMAGE_MODEL = 'imagen-3.0-generate-002';

// Available models
export const AVAILABLE_DIALOG_MODELS = [
  { 
    id: 'gemini-1.5-flash', 
    label: 'Gemini 1.5 Flash'
  },
  { 
    id: 'gemini-1.5-pro', 
    label: 'Gemini 1.5 Pro'
  }
];

export const AVAILABLE_IMAGE_MODELS = [
  { 
    id: 'imagen-3.0-generate-002', 
    label: 'Imagen 3' 
  }
];

// Audio processing settings
export const QUIET_THRESHOLD = 0.2; // Audio level threshold for considering silence
export const QUIET_DURATION = 3000; // ms of silence before considering speech complete
export const EXTENDED_QUIET_DURATION = 10000; // ms of silence before timeout

// API endpoints
export const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta';

// Generation configuration
export const DEFAULT_GENERATION_CONFIG = {
  temperature: 0.7,
  topP: 1,
  topK: 40,
  maxOutputTokens: 2048,
};
