import { reactive, computed, readonly } from 'vue';
import type { GameState, Scene, GameMessage, Mood, SceneOption, InventoryItem } from '../../types';
import { THEMES, MOODS } from '../../types';
import { DEFAULT_TEXT_MODEL, DEFAULT_LIVE_MODEL, DEFAULT_TTS_MODEL, DEFAULT_VOICE } from '../../config/ai-config';
import { StartSensitivity } from '@google/genai';

// Helper function to generate unique IDs
const generateId = (): string => Math.random().toString(36).substring(2, 11);

// Initialize default game state
const defaultState: GameState = {
  currentTheme: null,
  currentScene: null,
  scenes: [],
  companion: {
    name: 'Companion',
    description: 'Your loyal companion',
    personality: 'Friendly and helpful',
    values: ['Loyalty', 'Courage', 'Wisdom'],
    voiceId: 'default-voice',
    mood: 'neutral',
    relationshipScore: 0,
    memory: []
  },
  isNarrating: false,
  isCompanionSpeaking: false,
  isPlayerSpeaking: false,
  gameStarted: false,
  messages: [],
  inventory: [],
  flags: {},
  settings: {
    textModel: DEFAULT_TEXT_MODEL,
    liveModel: DEFAULT_LIVE_MODEL,
    ttsModel: DEFAULT_TTS_MODEL,
    voice: DEFAULT_VOICE,
    interruptSensitivity: StartSensitivity.START_SENSITIVITY_UNSPECIFIED
  }
};

// Create reactive state
const state = reactive<GameState>({ ...defaultState });

// Helper function to add a message to the game
const addMessage = (message: Omit<GameMessage, 'id' | 'timestamp'>): void => {
  const newMessage: GameMessage = {
    ...message,
    id: generateId(),
    timestamp: Date.now()
  };
  state.messages.push(newMessage);
  // Keep only the last 100 messages to prevent memory issues
  if (state.messages.length > 100) {
    state.messages.shift();
  }
};

// Computed properties are now defined directly in the useGameState composable

// Game state methods
const startGame = (themeId: string): void => {
  const theme = THEMES.find(t => t.id === themeId);
  if (!theme) return;
  
  // Reset state and set new theme
  Object.assign(state, {
    ...defaultState,
    currentTheme: { ...theme },
    companion: { ...theme.defaultCompanionTraits },
    gameStarted: true
  });
  
  // Add welcome message
  addMessage({
    type: 'narrator',
    content: `Welcome to your ${theme.name} adventure!`
  });
  
  // Generate initial scene
  generateInitialScene();
};

const selectOption = (option: SceneOption): void => {
  if (!state.currentScene) return;
  
  // Add player's choice to messages
  addMessage({
    type: 'player',
    content: option.text
  });
  
  // Update companion's mood based on the selected option
  if (option.relationshipImpact) {
    updateRelationship(option.relationshipImpact);
  }
  
  // Move to the next scene or handle the choice
  if (option.nextSceneId) {
    // In a real implementation, we'd load the next scene here
    // For now, we'll just update the current scene
    const nextScene: Scene = {
      id: option.nextSceneId,
      title: `Scene ${option.nextSceneId}`,
      description: `This is scene ${option.nextSceneId}. Your choice has led you here.`,
      imageUrl: '',
      options: [],
      isCurrent: true,
      visited: true
    };
    setCurrentScene(nextScene);
  }
};

const updateRelationship = (change: number): void => {
  if (!state.companion) return;
  
  state.companion.relationshipScore = Math.max(-100, Math.min(100, state.companion.relationshipScore + change));
  
  // Update companion's mood based on relationship score
  if (state.companion.relationshipScore > 50) {
    updateCompanionMood('happy');
  } else if (state.companion.relationshipScore < -30) {
    updateCompanionMood('angry');
  } else if (state.companion.relationshipScore < 0) {
    updateCompanionMood('sad');
  } else {
    updateCompanionMood('neutral');
  }
};

const startNarration = (): void => {
  state.isNarrating = true;
};

const generateInitialScene = (): void => {
  if (!state.currentTheme) return;
  
  // Create initial scene based on theme
  const initialScene: Scene = {
    id: 'start',
    title: 'The Beginning',
    description: `You find yourself at the start of your ${state.currentTheme.name} adventure. The air is filled with ${state.currentTheme.atmosphere}.`,
    imageUrl: state.currentTheme.defaultImage || '',
    options: [
      {
        id: 'look_around',
        text: 'Look around',
        nextSceneId: 'look_around',
        relationshipImpact: 0
      },
      {
        id: 'call_for_help',
        text: 'Call out for help',
        nextSceneId: 'call_for_help',
        relationshipImpact: 5
      },
      {
        id: 'search_belongings',
        text: 'Search your belongings',
        nextSceneId: 'search_belongings',
        relationshipImpact: -2
      }
    ],
    isCurrent: true,
    visited: true
  };
  
  setCurrentScene(initialScene);
  
  // Add scene description to messages
  addMessage({
    type: 'narrator',
    content: initialScene.description
  });
};

const setCurrentScene = (newScene: Scene): void => {
  // Mark current scene as not current if it exists
  if (state.currentScene) {
    state.currentScene.isCurrent = false;
  }
  
  // Create a new scene object to avoid reference issues
  const updatedScene: Scene = {
    ...newScene,
    isCurrent: true,
    visited: true
  };
  
  // Update the current scene
  state.currentScene = updatedScene;
  
  // Add scene description to messages
  addMessage({
    type: 'narrator',
    content: updatedScene.description
  });
  
  // Update scene in scenes array
  const sceneIndex = state.scenes.findIndex((scene: Scene) => scene.id === updatedScene.id);
  if (sceneIndex !== -1) {
    state.scenes[sceneIndex] = { ...updatedScene };
  } else {
    state.scenes.push({ ...updatedScene });
  }
};

const updateCompanionMood = (mood: Mood) => {
  if (state.companion) {
    // Create a new object to ensure reactivity
    state.companion = {
      ...state.companion,
      mood: mood
    };
    console.log('Companion mood updated to:', mood);
  } else {
    console.warn('Cannot update companion mood: no companion found');
  }
};

// Narration control
const stopNarration = (): void => {
  state.isNarrating = false;
};

const startCompanionSpeech = (): void => {
  state.isCompanionSpeaking = true;
};

const stopCompanionSpeech = (): void => {
  state.isCompanionSpeaking = false;
};

const startPlayerSpeech = (): void => {
  state.isPlayerSpeaking = true;
};

const stopPlayerSpeech = (): void => {
  state.isPlayerSpeaking = false;
};

// Mood helper functions
const getMoodEmoji = (mood: Mood): string => {
  return MOODS[mood]?.emoji || MOODS.neutral.emoji;
};

const getMoodDescription = (mood: Mood): string => {
  return MOODS[mood]?.description || MOODS.neutral.description;
};

// Reset the game to initial state
const resetGame = (): void => {
  Object.assign(state, { ...defaultState });
};

// Inventory and flag functions
const addToInventory = (item: Omit<InventoryItem, 'quantity'> & { quantity?: number }): void => {
  const existingItem = state.inventory.find(i => i.id === item.id);
  const quantity = item.quantity || 1;
  
  if (existingItem) {
    existingItem.quantity += quantity;
  } else {
    state.inventory.push({ ...item, quantity });
  }
  
  addMessage({
    type: 'system',
    content: `Added ${quantity}x ${item.name} to inventory`
  });
};

const removeFromInventory = (itemId: string, quantity = 1): boolean => {
  const itemIndex = state.inventory.findIndex(i => i.id === itemId);
  if (itemIndex === -1) return false;
  
  const item = state.inventory[itemIndex];
  if (item.quantity <= quantity) {
    state.inventory.splice(itemIndex, 1);
  } else {
    item.quantity -= quantity;
  }
  
  addMessage({
    type: 'system',
    content: `Removed ${quantity}x ${item.name} from inventory`
  });
  
  return true;
};

const setFlag = <T>(flag: string, value: T): void => {
  (state.flags as Record<string, T>)[flag] = value;
};

const getFlag = <T = unknown>(flag: string): T | undefined => {
  return state.flags[flag] as T | undefined;
};

const clearFlag = (flag: string): void => {
  delete state.flags[flag];
};

const hasItem = (itemId: string): boolean => {
  return state.inventory.some(item => item.id === itemId);
};

const addToCompanionMemory = (memory: string): void => {
  if (state.companion) {
    state.companion.memory.push(memory);
  }
};

// Export the composable with all state and methods
export const useGameState = () => ({
  // State
  state: readonly(state),
  currentTheme: computed(() => state.currentTheme),
  currentScene: computed(() => state.currentScene),
  companion: computed(() => state.companion),
  messages: computed(() => state.messages),
  inventory: computed(() => state.inventory),
  flags: computed(() => state.flags),
  isNarrating: computed(() => state.isNarrating),
  isCompanionSpeaking: computed(() => state.isCompanionSpeaking),
  isPlayerSpeaking: computed(() => state.isPlayerSpeaking),
  gameStarted: computed(() => state.gameStarted),
  settings: computed(() => state.settings),
  
  // Game flow methods
  startGame,
  generateInitialScene,
  selectOption,
  resetGame,
  
  // Companion interaction
  updateRelationship,
  updateCompanionMood,
  addToCompanionMemory,
  
  // Voice and narration
  startNarration,
  stopNarration,
  startCompanionSpeech,
  stopCompanionSpeech,
  startPlayerSpeech,
  stopPlayerSpeech,
  
  // Inventory and flags
  addToInventory,
  removeFromInventory,
  hasItem,
  setFlag,
  getFlag,
  clearFlag,
  
  // Message handling
  addMessage,

  // Mood helpers
  getMoodEmoji,
  getMoodDescription
});

export default useGameState;
