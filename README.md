# AI Adventurer

An interactive narrated adventure game with AI companions, built with Vue.js 3, TypeScript, and Google's Generative AI.

## Features

- Interactive storytelling with AI-powered narratives
- Multiple adventure themes (Fantasy, Sci-Fi, Mystery, etc.)
- Real-time AI companion interactions
- Voice synthesis and audio processing
- Responsive design with Tailwind CSS
- Progressive Web App (PWA) support

## Prerequisites

- Node.js 16+ and npm
- Google Generative AI API key
- Modern web browser with Web Audio API support

## Local Development Setup

1. Clone the repository:
   ```bash
   git clone <your-repo-url>
   cd ai-adventurer
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create environment file:
   ```bash
   cp .env.example .env
   ```

4. Add your Google AI API key to `.env`:
   ```
   VITE_GEMINI_API_KEY=your_api_key_here
   ```

5. Run the development server:
   ```bash
   npm run dev
   ```

## Deployment on Vercel

### Quick Deploy

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/yourusername/ai-adventurer)

### Manual Deployment

1. **Fork/Clone to GitHub**: Ensure your code is in a GitHub repository

2. **Connect to Vercel**:
   - Go to [vercel.com](https://vercel.com)
   - Sign up/login with your GitHub account
   - Click "New Project"
   - Import your GitHub repository

3. **Configure Environment Variables**:
   - In Vercel dashboard, go to your project settings
   - Navigate to "Environment Variables"
   - Add: `VITE_GEMINI_API_KEY` with your Google AI API key

4. **Deploy**:
   - Vercel will automatically build and deploy
   - Your app will be available at `https://your-project-name.vercel.app`

### Environment Variables for Vercel

Required:
- `VITE_GEMINI_API_KEY`: Your Google Generative AI API key

Optional:
- `NODE_ENV`: Set to "production" (Vercel sets this automatically)

## Getting Google AI API Key

1. Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated key and add it to your environment variables

## Project Structure

```
src/
├── components/          # Vue components
├── config/             # Configuration files
├── core/               # Core game logic
├── services/           # API services
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
├── App.vue             # Main app component
└── main.ts             # Application entry point
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## Configuration

### AI Models
Configure AI models in `src/config/ai-config.ts`:
- Text generation models (Gemini 2.0 Flash, etc.)
- Image generation models (Imagen 3, etc.)
- Voice synthesis options

### Audio Settings
Adjust audio processing in `src/config/ai-config.ts`:
- `QUIET_THRESHOLD`: Volume threshold for silence detection
- `QUIET_DURATION`: Silence duration before speech completion
- `VOICE_OPTIONS`: Available voice synthesis options

## Troubleshooting

### Common Issues
- **Microphone Access**: Ensure browser permissions for microphone
- **API Key**: Verify Google AI API key is correctly set
- **Build Errors**: Run `npm run type-check` to identify TypeScript issues

### Vercel Deployment Issues
- Check environment variables are set correctly
- Ensure build command is `npm run build`
- Verify output directory is set to `dist`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## License

This project is licensed under the MIT License.
