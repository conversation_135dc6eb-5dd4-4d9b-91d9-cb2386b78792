import { StartSensitivity } from '@google/genai';

// Audio processing thresholds
export const QUIET_THRESHOLD = 0.2; // Adjust this value based on testing
export const QUIET_DURATION = 3000; // milliseconds of silence before considering it as no audio
export const EXTENDED_QUIET_DURATION = 10000; // milliseconds of silence before extended quiet event

// Voice configuration
export const DEFAULT_VOICE = 'en-US-Neural2-J';
export const VOICE_OPTIONS = [
  { id: 'en-US-Neural2-J', label: 'English (US) - Male' },
  { id: 'en-US-Neural2-F', label: 'English (US) - Female' },
  { id: 'en-GB-Neural2-B', label: 'English (UK) - Male' },
  { id: 'en-GB-Neural2-C', label: 'English (UK) - Female' }
];

// Model configuration
export const DEFAULT_TEXT_MODEL = 'gemini-2.0-flash'; // For text generation/chat
export const DEFAULT_LIVE_MODEL = 'gemini-2.0-flash-live-001'; // For live audio conversations
export const DEFAULT_TTS_MODEL = 'gemini-2.5-flash-preview-tts'; // For narration TTS
export const DEFAULT_IMAGE_MODEL = 'imagen-3.0-generate-002';
export const DEFAULT_INTERRUPT_SENSITIVITY = StartSensitivity.START_SENSITIVITY_HIGH;

// Text Generation Models
export const AVAILABLE_TEXT_MODELS = [
  { 
    id: 'gemini-2.0-flash', 
    label: 'Gemini 2.0 Flash (Recommended)',
    description: 'Fast, versatile model with 1M token context, supports function calling and code execution'
  },
  { 
    id: 'gemini-2.5-pro-preview-06-05', 
    label: 'Gemini 2.5 Pro (Preview)',
    description: 'Advanced reasoning for complex problems, supports 1M token context, best for technical content'
  },
  { 
    id: 'gemini-2.0-flash-lite', 
    label: 'Gemini 2.0 Flash Lite',
    description: 'Cost-effective option for simpler tasks, supports function calling'
  }
];

// Text-to-Speech Models
export const AVAILABLE_TTS_MODELS = [
  {
    id: 'gemini-2.5-flash-preview-tts',
    label: '2.5 Flash TTS (Preview)',
    description: 'High-quality, price-performant TTS with 8K input tokens, ideal for general use'
  },
  {
    id: 'gemini-2.5-pro-preview-tts',
    label: '2.5 Pro TTS (Preview)',
    description: 'Highest quality TTS with 8K input tokens, best for premium audio experiences'
  },
  {
    id: 'gemini-2.5-flash-preview-native-audio-dialog',
    label: '2.5 Native Audio Dialog',
    description: 'Specialized for interactive conversations with audio I/O, 128K token limit'
  }
];

// Image Generation Models
export const AVAILABLE_IMAGE_MODELS = [
  { 
    id: 'imagen-3.0-generate-002', 
    label: 'Imagen 3',
    description: 'High-quality image generation with improved prompt understanding'
  },
  {
    id: 'gemini-2.0-flash-preview-image-generation',
    label: 'Gemini 2.0 Flash (Image Gen)',
    description: 'Multimodal model with image generation capabilities, 32K token limit'
  }
];

// Interrupt sensitivity options - only using valid values from StartSensitivity
export const INTERRUPT_SENSITIVITY_OPTIONS = [
  { value: StartSensitivity.START_SENSITIVITY_LOW, label: 'Harder to interrupt' },
  { value: StartSensitivity.START_SENSITIVITY_HIGH, label: 'Easier to interrupt' }
];