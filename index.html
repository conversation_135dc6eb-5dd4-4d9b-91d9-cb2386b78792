<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="AI Adventurer - An interactive narrated adventure with AI companions" />
    <meta name="theme-color" content="#1a202c" />
    <title>AI Adventurer</title>
    
    <!-- Preconnect to external resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Grandstander:ital,wght@0,100..900;1,100..900&family=Roboto+Mono:ital,wght@0,100..700;1,100..700&display=swap" rel="stylesheet" />
    
    <!-- Remove Tailwind CDN and use local CSS -->
    <link rel="stylesheet" href="/src/index.css">
    
    <!-- PWA manifest -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- iOS meta tags -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="AI Adventurer" />

    <!-- Android/Chrome -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="application-name" content="AI Adventurer" />
    <meta name="msapplication-TileColor" content="#1a202c" />

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:title" content="AI Adventurer" />
    <meta name="twitter:description" content="An interactive narrated adventure with AI companions" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="AI Adventurer" />
    <meta property="og:description" content="An interactive narrated adventure with AI companions" />
    <meta property="og:site_name" content="AI Adventurer" />
  </head>
  <body class="bg-gray-900 text-gray-100 min-h-screen">
    <div id="app">
      <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; display: flex; justify-content: center; align-items: center; background: #1a202c; z-index: 9999;" id="loading">
        <div style="color: white; font-size: 1.5rem; font-family: sans-serif;">Loading AI Adventurer...</div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
    <script>
      // Hide loading screen when app is mounted
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
          const loading = document.getElementById('loading');
          if (loading) loading.style.display = 'none';
        }, 1000);
      });
    </script>
  </body>
</html>
