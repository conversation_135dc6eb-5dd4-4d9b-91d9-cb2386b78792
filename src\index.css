@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  body {
    @apply bg-gray-900 text-gray-100 font-sans leading-relaxed;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold leading-tight tracking-tight;
  }

  h1 { @apply text-4xl md:text-5xl; }
  h2 { @apply text-3xl md:text-4xl; }
  h3 { @apply text-2xl md:text-3xl; }
  h4 { @apply text-xl md:text-2xl; }
  h5 { @apply text-lg md:text-xl; }
  h6 { @apply text-base md:text-lg; }

  a {
    @apply text-purple-400 hover:text-purple-300 transition-colors;
  }

  button, .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-purple-600 hover:bg-purple-700 text-white;
  }

  .btn-secondary {
    @apply bg-gray-700 hover:bg-gray-600 text-white;
  }

  .btn-outline {
    @apply border border-gray-600 hover:border-purple-500 text-gray-300 hover:text-white;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  @apply w-2 h-2;
}

::-webkit-scrollbar-track {
  @apply bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-600 rounded-full hover:bg-gray-500;
}

/* Custom utilities */
.glass {
  @apply bg-white/5 backdrop-blur-sm border border-white/10;
}

/* Fix for Vue transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Custom form styles */
input[type="text"],
input[type="email"],
input[type="password"],
textarea,
select {
  @apply w-full px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 outline-none transition-colors duration-200;
}

/* Custom checkbox */
input[type="checkbox"] {
  @apply h-5 w-5 rounded border-gray-600 bg-gray-700 text-purple-500 focus:ring-purple-500 focus:ring-offset-gray-800;
}

/* Custom radio */
input[type="radio"] {
  @apply h-5 w-5 border-gray-600 bg-gray-700 text-purple-500 focus:ring-purple-500 focus:ring-offset-gray-800;
}

/* Custom components */
@layer components {
  /* Custom tooltip */
  .tooltip {
    @apply invisible absolute;
  }

  .has-tooltip:hover .tooltip {
    @apply visible z-50;
  }

  /* Custom markdown content */
  .markdown-content {
    @apply prose prose-invert max-w-none;
  }

  .markdown-content h1,
  .markdown-content h2,
  .markdown-content h3,
  .markdown-content h4,
  .markdown-content h5,
  .markdown-content h6 {
    @apply text-white;
  }

  .markdown-content p {
    @apply my-4;
  }

  .markdown-content ul,
  .markdown-content ol {
    @apply my-4 pl-6;
  }

  .markdown-content ul {
    @apply list-disc;
  }

  .markdown-content ol {
    @apply list-decimal;
  }

  .markdown-content a {
    @apply text-purple-400 hover:underline;
  }

  .markdown-content code {
    @apply bg-gray-800 px-1.5 py-0.5 rounded text-sm font-mono;
  }

  .markdown-content pre {
    @apply bg-gray-800 p-4 rounded-lg overflow-x-auto my-4;
  }

  .markdown-content pre code {
    @apply bg-transparent p-0;
  }

  .markdown-content blockquote {
    @apply border-l-4 border-gray-600 pl-4 italic text-gray-300 my-4;
  }
}
