<template>
  <div class="app-container">
    <div v-if="error" class="error-overlay">
      <h2>An error occurred</h2>
      <pre class="error-message">{{ error }}</pre>
      <button @click="reloadApp" class="btn btn-primary mt-4">Reload App</button>
    </div>
    
    <!-- Debug info -->
    <div v-if="showDebug" class="debug-info">
      <div>App mounted: {{ isMounted ? 'Yes' : 'No' }}</div>
      <div>Game started: {{ state.gameStarted ? 'Yes' : 'No' }}</div>
      <div>Loading: {{ isLoading ? 'Yes' : 'No' }}</div>
    </div>

    <!-- Theme Selection Screen -->
    <ThemeSelector 
      v-if="!state.gameStarted" 
      @theme-selected="handleThemeSelected" 
    />
    
    <!-- Adventure Screen -->
    <AdventureScreen v-else />
    
    <!-- Loading Overlay -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">Preparing your adventure...</div>
    </div>
    
    <!-- Audio Elements -->
    <audio ref="backgroundMusic" loop>
      <source :src="backgroundMusicSrc" type="audio/mpeg">
    </audio>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onErrorCaptured } from 'vue';
import useGameState from './core/state/gameState';  // Changed from named import to default import
import ThemeSelector from './components/ThemeSelector.vue';
import AdventureScreen from './components/AdventureScreen.vue';

export default defineComponent({
  name: 'App',
  
  components: {
    ThemeSelector,
    AdventureScreen
  },
  
  setup() {
    const { state, startGame, resetGame } = useGameState();
    const isLoading = ref(false);
    const backgroundMusic = ref<HTMLAudioElement | null>(null);
    const backgroundMusicSrc = ref('');
    const error = ref<Error | null>(null);
    const isMounted = ref(false);
    const showDebug = ref(true); // Set to false in production

    // Error handling
    onErrorCaptured((err) => {
      console.error('App error:', err);
      error.value = err;
      return false;
    });

    onMounted(() => {
      console.log('App component mounted');
      isMounted.value = true;
      
      // Force show debug in development
      if (import.meta.env.DEV) {
        showDebug.value = true;
      }
    });

    // Handle theme selection
    const handleThemeSelected = async (themeId: string) => {
      try {
        console.log('Theme selected, starting game...', themeId);
        isLoading.value = true;
        
        // Start the game with the selected theme
        startGame(themeId);
        
        // Simulate loading time for assets
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        console.log('Game started with theme:', themeId);
      } catch (err) {
        console.error('Error in handleThemeSelected:', err);
        error.value = err as Error;
        // If there's an error, reset the game state
        resetGame();
        throw err; // Re-throw to allow parent components to handle if needed
      } finally {
        isLoading.value = false;
      }
    };

    const reloadApp = () => {
      window.location.reload();
    };

    return {
      state,
      isLoading,
      backgroundMusic,
      backgroundMusicSrc,
      handleThemeSelected,
      error,
      reloadApp,
      isMounted,
      showDebug
    };
  },
  errorCaptured(err, vm, info) {
    console.error('Error captured:', { err, vm, info });
    return false;
  }
});
</script>

<style scoped>
.app-container {
  position: relative;
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top-color: #9f7aea;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

.error-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #1a202c;
  color: #f56565;
  padding: 2rem;
  z-index: 2000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.error-message {
  background: #2d3748;
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 1rem 0;
  max-width: 800px;
  max-height: 300px;
  overflow: auto;
  text-align: left;
  font-family: monospace;
  white-space: pre-wrap;
}

.debug-info {
  position: fixed;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.7);
  color: #68d391;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-family: monospace;
  font-size: 0.9rem;
  z-index: 1000;
  border: 1px solid #2d3748;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>

<style>
/* Global Styles */
:root {
  --primary: #9f7aea;
  --primary-dark: #805ad5;
  --secondary: #4fd1c5;
  --accent: #f6ad55;
  --background: #1a202c;
  --text: #e2e8f0;
  --text-secondary: #a0aec0;
  --success: #48bb78;
  --danger: #f56565;
  --warning: #ecc94b;
  --info: #4299e1;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #app {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-family: 'Grandstander', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--text);
  background-color: var(--background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  position: relative;
  display: flex;
  flex-direction: column;
}

.app-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  color: white;
  font-size: 1.2rem;
  margin-top: 1rem;
  text-align: center;
  max-width: 80%;
}

/* Animations */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* Utility Classes */
.text-gradient {
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* Responsive typography */
html {
  font-size: 16px;
}

@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 12px;
  }
}
</style>
